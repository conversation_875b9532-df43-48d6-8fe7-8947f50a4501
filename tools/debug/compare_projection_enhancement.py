#!/usr/bin/env python3
"""
点云密度投影增强效果对比工具

此脚本用于对比原始和增强版点云密度投影的效果，验证墙体边缘连续性改善情况。
"""

import os
import sys
import argparse
import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path
import json
import logging

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'dataset_converters'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'dataset_converters', 'stru3d'))

from dataset_converters.common_utils import read_scene_pc
from dataset_converters.stru3d.stru3d_utils import generate_density, generate_density_enhanced
from configs.mask2former_config import set_img_size

def setup_logging():
    """设置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def analyze_edge_continuity(density_map, threshold=0.1):
    """
    分析密度图的边缘连续性
    
    返回:
        edge_density: 边缘密度 (边缘像素数 / 总像素数)
        avg_edge_length: 平均边缘长度
        edge_fragments: 边缘片段数量
    """
    # 转换为uint8进行边缘检测
    density_uint8 = (density_map * 255).astype(np.uint8)
    
    # Canny边缘检测
    edges = cv2.Canny(density_uint8, 50, 150)
    
    # 计算边缘密度
    edge_pixels = np.sum(edges > 0)
    total_pixels = edges.shape[0] * edges.shape[1]
    edge_density = edge_pixels / total_pixels
    
    # 查找轮廓来分析边缘片段
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if len(contours) > 0:
        edge_lengths = [cv2.arcLength(contour, False) for contour in contours]
        avg_edge_length = np.mean(edge_lengths)
        edge_fragments = len(contours)
    else:
        avg_edge_length = 0
        edge_fragments = 0
    
    return {
        'edge_density': edge_density,
        'avg_edge_length': avg_edge_length,
        'edge_fragments': edge_fragments,
        'total_edge_pixels': edge_pixels
    }

def calculate_wall_prominence(density_map):
    """
    计算墙体突出度指标
    
    返回:
        wall_prominence: 墙体突出度分数
    """
    # 计算密度图的统计信息
    non_zero_pixels = density_map[density_map > 0.01]
    
    if len(non_zero_pixels) == 0:
        return 0.0
    
    # 高密度像素比例 (>0.5的像素)
    high_density_ratio = np.sum(density_map > 0.5) / np.sum(density_map > 0.01)
    
    # 密度方差 (更高的方差表示更好的对比度)
    density_variance = np.var(non_zero_pixels)
    
    # 综合评分
    wall_prominence = high_density_ratio * 0.6 + min(density_variance, 1.0) * 0.4
    
    return wall_prominence

def compare_projections(point_cloud_path, output_dir, scene_id):
    """
    对比原始和增强版投影效果
    """
    logger = logging.getLogger(__name__)
    
    # 读取点云数据
    logger.info(f"读取点云数据: {point_cloud_path}")
    points = read_scene_pc(point_cloud_path)
    xyz = points[:, :3]
    
    # 应用Z坐标过滤（与主脚本保持一致）
    z_min, z_max = np.min(xyz[:, 2]), np.max(xyz[:, 2])
    z_range = z_max - z_min
    
    if z_range > 0.05:
        z_filter_margin = z_range * 0.25
        z_filter = (xyz[:, 2] > z_min + z_filter_margin) & (xyz[:, 2] < z_max - z_filter_margin)
        xyz = xyz[z_filter]
    
    logger.info(f"点云数量: {len(xyz)}")
    
    # 生成原始密度图
    logger.info("生成原始密度图...")
    density_original, norm_dict_original = generate_density(xyz, width=set_img_size, height=set_img_size)
    
    # 生成增强密度图
    logger.info("生成增强密度图...")
    density_enhanced, norm_dict_enhanced = generate_density_enhanced(
        xyz, 
        width=set_img_size, 
        height=set_img_size,
        enable_continuity_enhancement=True,
        enable_morphological_ops=True,
        enable_wall_enhancement=True,
        continuity_method='interpolation',
        morphological_operation='closing',
        wall_enhancement_method='gradient',
        morphological_kernel_size=3,
        morphological_iterations=1
    )
    
    # 分析效果
    logger.info("分析投影效果...")
    
    # 边缘连续性分析
    edge_analysis_original = analyze_edge_continuity(density_original)
    edge_analysis_enhanced = analyze_edge_continuity(density_enhanced)
    
    # 墙体突出度分析
    wall_prominence_original = calculate_wall_prominence(density_original)
    wall_prominence_enhanced = calculate_wall_prominence(density_enhanced)
    
    # 保存结果
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存密度图
    cv2.imwrite(os.path.join(output_dir, f"{scene_id}_original.png"), 
                (density_original * 255).astype(np.uint8))
    cv2.imwrite(os.path.join(output_dir, f"{scene_id}_enhanced.png"), 
                (density_enhanced * 255).astype(np.uint8))
    
    # 生成对比图
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 原始密度图
    axes[0, 0].imshow(density_original, cmap='hot')
    axes[0, 0].set_title(f'Original Projection\nWall Prominence: {wall_prominence_original:.3f}')
    axes[0, 0].axis('off')
    
    # 增强密度图
    axes[0, 1].imshow(density_enhanced, cmap='hot')
    axes[0, 1].set_title(f'Enhanced Projection\nWall Prominence: {wall_prominence_enhanced:.3f}')
    axes[0, 1].axis('off')
    
    # 边缘检测对比
    edges_original = cv2.Canny((density_original * 255).astype(np.uint8), 50, 150)
    edges_enhanced = cv2.Canny((density_enhanced * 255).astype(np.uint8), 50, 150)
    
    axes[1, 0].imshow(edges_original, cmap='gray')
    axes[1, 0].set_title(f'Original Edges\nFragments: {edge_analysis_original["edge_fragments"]}')
    axes[1, 0].axis('off')
    
    axes[1, 1].imshow(edges_enhanced, cmap='gray')
    axes[1, 1].set_title(f'Enhanced Edges\nFragments: {edge_analysis_enhanced["edge_fragments"]}')
    axes[1, 1].axis('off')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f"{scene_id}_comparison.png"), dpi=150, bbox_inches='tight')
    plt.close()
    
    # 保存分析结果
    analysis_results = {
        'scene_id': scene_id,
        'point_count': len(xyz),
        'original': {
            'edge_analysis': edge_analysis_original,
            'wall_prominence': wall_prominence_original
        },
        'enhanced': {
            'edge_analysis': edge_analysis_enhanced,
            'wall_prominence': wall_prominence_enhanced
        },
        'improvements': {
            'edge_density_improvement': edge_analysis_enhanced['edge_density'] - edge_analysis_original['edge_density'],
            'edge_fragments_reduction': edge_analysis_original['edge_fragments'] - edge_analysis_enhanced['edge_fragments'],
            'wall_prominence_improvement': wall_prominence_enhanced - wall_prominence_original
        }
    }
    
    with open(os.path.join(output_dir, f"{scene_id}_analysis.json"), 'w') as f:
        json.dump(analysis_results, f, indent=2)
    
    # 打印结果摘要
    logger.info("=" * 60)
    logger.info(f"场景 {scene_id} 投影增强效果分析")
    logger.info("=" * 60)
    logger.info(f"点云数量: {len(xyz)}")
    logger.info(f"边缘密度改善: {analysis_results['improvements']['edge_density_improvement']:.4f}")
    logger.info(f"边缘片段减少: {analysis_results['improvements']['edge_fragments_reduction']}")
    logger.info(f"墙体突出度改善: {analysis_results['improvements']['wall_prominence_improvement']:.4f}")
    
    return analysis_results

def main():
    parser = argparse.ArgumentParser(description='Compare original and enhanced point cloud projections')
    parser.add_argument('--point_cloud', required=True, help='Path to point cloud file (.ply)')
    parser.add_argument('--output_dir', default='tools/debug/projection_comparison', help='Output directory for comparison results')
    parser.add_argument('--scene_id', help='Scene ID for naming (default: extracted from filename)')
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging()
    
    # 提取场景ID
    if args.scene_id:
        scene_id = args.scene_id
    else:
        scene_id = Path(args.point_cloud).stem
    
    logger.info(f"开始对比投影增强效果 - 场景: {scene_id}")
    
    # 执行对比分析
    try:
        results = compare_projections(args.point_cloud, args.output_dir, scene_id)
        logger.info(f"对比分析完成！结果保存到: {args.output_dir}")
        
        # 输出改善情况
        improvements = results['improvements']
        if improvements['wall_prominence_improvement'] > 0:
            logger.info("✅ 墙体突出度有所改善")
        if improvements['edge_fragments_reduction'] > 0:
            logger.info("✅ 边缘连续性有所改善")
        if improvements['edge_density_improvement'] > 0:
            logger.info("✅ 边缘密度有所提升")
            
    except Exception as e:
        logger.error(f"对比分析失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
