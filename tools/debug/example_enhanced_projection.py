#!/usr/bin/env python3
"""
增强版点云密度投影使用示例

此脚本展示如何使用增强版投影功能处理单个场景。
"""

import os
import sys
import argparse
import numpy as np

def main():
    """
    主函数 - 展示增强版投影的使用方法
    """
    print("🚀 增强版点云密度投影使用示例")
    print("=" * 60)
    
    # 示例1: 基本使用方法
    print("\n📖 示例1: 基本使用方法")
    print("-" * 30)
    print("在命令行中运行以下命令来使用增强版投影:")
    print()
    print("python tools/dataset_converters/stru3d/generate_coco_stru3d.py \\")
    print("    --data_root /path/to/Structured3D \\")
    print("    --output /path/to/output \\")
    print("    --use_enhanced_projection \\")
    print("    --continuity_enhancement \\")
    print("    --morphological_ops \\")
    print("    --wall_enhancement")
    print()
    
    # 示例2: 参数配置
    print("📖 示例2: 详细参数配置")
    print("-" * 30)
    print("# 对于稀疏点云场景的推荐配置:")
    print("python tools/dataset_converters/stru3d/generate_coco_stru3d.py \\")
    print("    --data_root /path/to/Structured3D \\")
    print("    --output /path/to/output \\")
    print("    --use_enhanced_projection \\")
    print("    --continuity_method combined \\")
    print("    --morphological_operation closing \\")
    print("    --morphological_kernel_size 5 \\")
    print("    --wall_enhancement_method gradient")
    print()
    
    # 示例3: 效果对比
    print("📖 示例3: 效果对比测试")
    print("-" * 30)
    print("# 对比单个场景的增强效果:")
    print("python tools/debug/compare_projection_enhancement.py \\")
    print("    --point_cloud /path/to/scene/point_cloud.ply \\")
    print("    --output_dir tools/debug/comparison_results \\")
    print("    --scene_id scene_001")
    print()
    print("# 批量测试多个场景:")
    print("python tools/debug/test_projection_enhancement.py \\")
    print("    --data_root /path/to/Structured3D \\")
    print("    --output_dir tools/debug/batch_test_results \\")
    print("    --max_scenes 10")
    print()
    
    # 示例4: 不同配置的效果
    print("📖 示例4: 不同配置的适用场景")
    print("-" * 30)
    
    configurations = [
        {
            "name": "快速处理模式",
            "description": "仅启用形态学操作，处理速度快",
            "params": [
                "--use_enhanced_projection",
                "--morphological_ops",
                "--morphological_operation closing"
            ]
        },
        {
            "name": "平衡模式",
            "description": "平衡处理速度和效果质量",
            "params": [
                "--use_enhanced_projection",
                "--continuity_enhancement",
                "--continuity_method interpolation",
                "--morphological_ops",
                "--morphological_operation closing",
                "--wall_enhancement",
                "--wall_enhancement_method gradient"
            ]
        },
        {
            "name": "高质量模式",
            "description": "最佳效果，适用于重要场景",
            "params": [
                "--use_enhanced_projection",
                "--continuity_enhancement",
                "--continuity_method combined",
                "--morphological_ops",
                "--morphological_operation closing",
                "--morphological_kernel_size 5",
                "--wall_enhancement",
                "--wall_enhancement_method gradient"
            ]
        }
    ]
    
    for i, config in enumerate(configurations, 1):
        print(f"\n{i}. {config['name']}")
        print(f"   描述: {config['description']}")
        print("   参数:")
        for param in config['params']:
            print(f"     {param}")
    
    # 功能说明
    print("\n📖 功能详解")
    print("-" * 30)
    
    features = [
        {
            "name": "连续性增强 (--continuity_enhancement)",
            "methods": {
                "interpolation": "线性插值，适用于规整结构",
                "gaussian_noise": "高斯噪声，适用于稀疏点云",
                "combined": "组合方法，效果最佳"
            }
        },
        {
            "name": "形态学操作 (--morphological_ops)",
            "methods": {
                "closing": "闭运算，填补孔洞（推荐）",
                "dilation": "膨胀，加粗边缘",
                "opening": "开运算，去除噪声",
                "erosion": "腐蚀，细化边缘"
            }
        },
        {
            "name": "墙体增强 (--wall_enhancement)",
            "methods": {
                "gradient": "梯度增强，突出边缘（推荐）",
                "adaptive_threshold": "自适应阈值",
                "edge_detection": "边缘检测"
            }
        }
    ]
    
    for feature in features:
        print(f"\n• {feature['name']}")
        for method, desc in feature['methods'].items():
            print(f"  - {method}: {desc}")
    
    # 性能提示
    print("\n💡 性能优化提示")
    print("-" * 30)
    print("• 内存使用: 增强功能会增加 1.5-3 倍内存使用")
    print("• 处理时间: 总体增加 35-90% 的处理时间")
    print("• 推荐配置: 根据硬件资源选择合适的增强级别")
    print("• 批量处理: 可以使用 --max_scenes 限制处理数量")
    
    # 兼容性说明
    print("\n🔧 兼容性说明")
    print("-" * 30)
    print("• COCO格式: 完全兼容现有的COCO数据格式")
    print("• 坐标变换: 保持与原始流程的坐标兼容性")
    print("• 下游任务: 与Mask2Former等模型无缝集成")
    print("• C++流程: 支持与C++点云投影的坐标映射")
    
    # 故障排除
    print("\n🔍 常见问题解决")
    print("-" * 30)
    print("1. 内存不足:")
    print("   - 减少 --max_scenes 参数")
    print("   - 使用较小的 --morphological_kernel_size")
    print("   - 避免使用 combined 连续性增强")
    print()
    print("2. 处理速度慢:")
    print("   - 使用 interpolation 而非 combined")
    print("   - 减少 --morphological_iterations")
    print("   - 仅启用必要的增强功能")
    print()
    print("3. 效果不理想:")
    print("   - 尝试不同的参数组合")
    print("   - 使用对比工具分析问题")
    print("   - 检查原始点云质量")
    
    print("\n" + "=" * 60)
    print("✅ 示例完成！请根据需要选择合适的配置。")
    print("\n📚 更多信息请参考:")
    print("   - tools/debug/enhanced_projection_guide.md")
    print("   - tools/debug/compare_projection_enhancement.py")
    print("   - tools/debug/test_projection_enhancement.py")

if __name__ == "__main__":
    main()
