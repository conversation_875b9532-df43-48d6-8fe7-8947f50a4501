#!/usr/bin/env python3
"""
测试增强版点云密度投影功能

此脚本用于快速测试增强版投影功能是否正常工作。
"""

import os
import sys
import numpy as np
import cv2
import matplotlib.pyplot as plt

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'dataset_converters'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'dataset_converters', 'stru3d'))

from stru3d.stru3d_utils import generate_density, generate_density_enhanced

def create_test_point_cloud():
    """
    创建测试用的点云数据，模拟房间结构
    """
    points = []
    
    # 创建一个简单的房间结构
    # 外墙
    for x in np.linspace(0, 10, 50):
        points.append([x, 0, 1])  # 底边
        points.append([x, 10, 1])  # 顶边
    
    for y in np.linspace(0, 10, 50):
        points.append([0, y, 1])  # 左边
        points.append([10, y, 1])  # 右边
    
    # 内部墙体（模拟不连续的情况）
    for x in np.linspace(2, 8, 20):
        if np.random.random() > 0.3:  # 30%的点缺失，模拟稀疏情况
            points.append([x, 5, 1])
    
    # 添加一些噪声点
    for _ in range(100):
        x = np.random.uniform(1, 9)
        y = np.random.uniform(1, 9)
        z = np.random.uniform(0.5, 1.5)
        points.append([x, y, z])
    
    return np.array(points)

def test_enhancement_functions():
    """
    测试各个增强功能
    """
    print("🧪 测试增强版点云密度投影功能")
    print("=" * 50)
    
    # 创建测试点云
    print("📊 创建测试点云...")
    point_cloud = create_test_point_cloud()
    print(f"   点云数量: {len(point_cloud)}")
    
    # 测试原始投影
    print("🔍 测试原始投影...")
    try:
        density_original, norm_dict_original = generate_density(point_cloud, width=256, height=256)
        print(f"   ✅ 原始投影成功 - 密度图形状: {density_original.shape}")
        print(f"   密度范围: [{density_original.min():.3f}, {density_original.max():.3f}]")
    except Exception as e:
        print(f"   ❌ 原始投影失败: {e}")
        return False
    
    # 测试增强投影 - 基础版本
    print("🚀 测试增强投影 (基础版本)...")
    try:
        density_enhanced_basic, norm_dict_enhanced = generate_density_enhanced(
            point_cloud, width=256, height=256,
            enable_continuity_enhancement=False,
            enable_morphological_ops=False,
            enable_wall_enhancement=False
        )
        print(f"   ✅ 基础增强投影成功 - 密度图形状: {density_enhanced_basic.shape}")
        print(f"   密度范围: [{density_enhanced_basic.min():.3f}, {density_enhanced_basic.max():.3f}]")
    except Exception as e:
        print(f"   ❌ 基础增强投影失败: {e}")
        return False
    
    # 测试增强投影 - 连续性增强
    print("🔗 测试连续性增强...")
    try:
        density_continuity, _ = generate_density_enhanced(
            point_cloud, width=256, height=256,
            enable_continuity_enhancement=True,
            enable_morphological_ops=False,
            enable_wall_enhancement=False,
            continuity_method='interpolation'
        )
        print(f"   ✅ 连续性增强成功 - 密度图形状: {density_continuity.shape}")
        print(f"   密度范围: [{density_continuity.min():.3f}, {density_continuity.max():.3f}]")
    except Exception as e:
        print(f"   ❌ 连续性增强失败: {e}")
        return False
    
    # 测试增强投影 - 形态学操作
    print("🔧 测试形态学操作...")
    try:
        density_morphological, _ = generate_density_enhanced(
            point_cloud, width=256, height=256,
            enable_continuity_enhancement=False,
            enable_morphological_ops=True,
            enable_wall_enhancement=False,
            morphological_operation='closing'
        )
        print(f"   ✅ 形态学操作成功 - 密度图形状: {density_morphological.shape}")
        print(f"   密度范围: [{density_morphological.min():.3f}, {density_morphological.max():.3f}]")
    except Exception as e:
        print(f"   ❌ 形态学操作失败: {e}")
        return False
    
    # 测试增强投影 - 墙体增强
    print("🏠 测试墙体增强...")
    try:
        density_wall_enhanced, _ = generate_density_enhanced(
            point_cloud, width=256, height=256,
            enable_continuity_enhancement=False,
            enable_morphological_ops=False,
            enable_wall_enhancement=True,
            wall_enhancement_method='gradient'
        )
        print(f"   ✅ 墙体增强成功 - 密度图形状: {density_wall_enhanced.shape}")
        print(f"   密度范围: [{density_wall_enhanced.min():.3f}, {density_wall_enhanced.max():.3f}]")
    except Exception as e:
        print(f"   ❌ 墙体增强失败: {e}")
        return False
    
    # 测试增强投影 - 全功能
    print("🎯 测试全功能增强...")
    try:
        density_full_enhanced, _ = generate_density_enhanced(
            point_cloud, width=256, height=256,
            enable_continuity_enhancement=True,
            enable_morphological_ops=True,
            enable_wall_enhancement=True,
            continuity_method='combined',
            morphological_operation='closing',
            wall_enhancement_method='gradient',
            morphological_kernel_size=3,
            morphological_iterations=1
        )
        print(f"   ✅ 全功能增强成功 - 密度图形状: {density_full_enhanced.shape}")
        print(f"   密度范围: [{density_full_enhanced.min():.3f}, {density_full_enhanced.max():.3f}]")
    except Exception as e:
        print(f"   ❌ 全功能增强失败: {e}")
        return False
    
    # 生成可视化对比
    print("📊 生成可视化对比...")
    try:
        output_dir = "tools/debug/enhancement_test_output"
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建对比图
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        
        # 原始投影
        axes[0, 0].imshow(density_original, cmap='hot')
        axes[0, 0].set_title('Original Projection')
        axes[0, 0].axis('off')
        
        # 连续性增强
        axes[0, 1].imshow(density_continuity, cmap='hot')
        axes[0, 1].set_title('Continuity Enhanced')
        axes[0, 1].axis('off')
        
        # 形态学增强
        axes[0, 2].imshow(density_morphological, cmap='hot')
        axes[0, 2].set_title('Morphological Enhanced')
        axes[0, 2].axis('off')
        
        # 墙体增强
        axes[1, 0].imshow(density_wall_enhanced, cmap='hot')
        axes[1, 0].set_title('Wall Enhanced')
        axes[1, 0].axis('off')
        
        # 全功能增强
        axes[1, 1].imshow(density_full_enhanced, cmap='hot')
        axes[1, 1].set_title('Full Enhanced')
        axes[1, 1].axis('off')
        
        # 差异图
        diff = density_full_enhanced - density_original
        im = axes[1, 2].imshow(diff, cmap='RdBu', vmin=-0.5, vmax=0.5)
        axes[1, 2].set_title('Enhancement Difference')
        axes[1, 2].axis('off')
        plt.colorbar(im, ax=axes[1, 2], fraction=0.046, pad=0.04)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'enhancement_test_comparison.png'), 
                   dpi=150, bbox_inches='tight')
        plt.close()
        
        # 保存密度图
        cv2.imwrite(os.path.join(output_dir, 'original.png'), 
                   (density_original * 255).astype(np.uint8))
        cv2.imwrite(os.path.join(output_dir, 'enhanced_full.png'), 
                   (density_full_enhanced * 255).astype(np.uint8))
        
        print(f"   ✅ 可视化结果保存到: {output_dir}")
        
    except Exception as e:
        print(f"   ⚠️  可视化生成失败: {e}")
    
    print("\n🎉 所有测试通过！增强功能正常工作。")
    return True

def test_parameter_variations():
    """
    测试不同参数组合
    """
    print("\n🔬 测试参数变化效果...")
    
    point_cloud = create_test_point_cloud()
    
    # 测试不同的连续性增强方法
    methods = ['interpolation', 'gaussian_noise', 'combined']
    for method in methods:
        try:
            density, _ = generate_density_enhanced(
                point_cloud, width=128, height=128,
                enable_continuity_enhancement=True,
                continuity_method=method
            )
            print(f"   ✅ 连续性方法 '{method}' 测试成功")
        except Exception as e:
            print(f"   ❌ 连续性方法 '{method}' 测试失败: {e}")
    
    # 测试不同的形态学操作
    operations = ['closing', 'dilation', 'opening', 'erosion']
    for operation in operations:
        try:
            density, _ = generate_density_enhanced(
                point_cloud, width=128, height=128,
                enable_morphological_ops=True,
                morphological_operation=operation
            )
            print(f"   ✅ 形态学操作 '{operation}' 测试成功")
        except Exception as e:
            print(f"   ❌ 形态学操作 '{operation}' 测试失败: {e}")
    
    # 测试不同的墙体增强方法
    wall_methods = ['gradient', 'adaptive_threshold', 'edge_detection']
    for method in wall_methods:
        try:
            density, _ = generate_density_enhanced(
                point_cloud, width=128, height=128,
                enable_wall_enhancement=True,
                wall_enhancement_method=method
            )
            print(f"   ✅ 墙体增强方法 '{method}' 测试成功")
        except Exception as e:
            print(f"   ❌ 墙体增强方法 '{method}' 测试失败: {e}")

def main():
    """
    主测试函数
    """
    print("🚀 开始测试增强版点云密度投影功能")
    print("=" * 60)
    
    # 基础功能测试
    if not test_enhancement_functions():
        print("❌ 基础功能测试失败")
        return 1
    
    # 参数变化测试
    test_parameter_variations()
    
    print("\n" + "=" * 60)
    print("✅ 所有测试完成！增强功能可以正常使用。")
    print("\n💡 使用建议:")
    print("   1. 对于稀疏点云，推荐启用连续性增强")
    print("   2. 对于边缘不连续问题，推荐使用形态学操作")
    print("   3. 对于墙体不够突出的问题，推荐启用墙体增强")
    print("   4. 可以根据具体场景调整参数获得最佳效果")
    
    return 0

if __name__ == "__main__":
    exit(main())
