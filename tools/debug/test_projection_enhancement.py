#!/usr/bin/env python3
"""
批量测试点云密度投影增强效果

此脚本用于批量测试多个场景的投影增强效果，并生成统计报告。
"""

import os
import sys
import argparse
import numpy as np
import json
import logging
from pathlib import Path
from tqdm import tqdm
import matplotlib.pyplot as plt

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'dataset_converters'))

from compare_projection_enhancement import compare_projections

def setup_logging():
    """设置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def find_point_cloud_files(data_root, max_scenes=None):
    """
    查找点云文件
    
    参数:
        data_root: Structured3D数据根目录
        max_scenes: 最大场景数量限制
    
    返回:
        点云文件路径列表
    """
    point_cloud_files = []
    
    # 遍历数据目录
    data_parts = [d for d in os.listdir(data_root)
                  if os.path.isdir(os.path.join(data_root, d))
                  and d.startswith('Structured3D_panorama')]
    data_parts = sorted(data_parts)
    
    for part in data_parts:
        part_path = os.path.join(data_root, part, 'Structured3D')
        if os.path.exists(part_path):
            scenes = [s for s in os.listdir(part_path)
                     if os.path.isdir(os.path.join(part_path, s))]
            scenes = sorted(scenes)
            
            for scene in scenes:
                scene_path = os.path.join(part_path, scene)
                ply_path = os.path.join(scene_path, 'point_cloud.ply')
                
                if os.path.exists(ply_path):
                    scene_id = scene.split('_')[-1]
                    point_cloud_files.append((ply_path, scene_id))
                    
                    if max_scenes and len(point_cloud_files) >= max_scenes:
                        return point_cloud_files
    
    return point_cloud_files

def generate_summary_report(results_list, output_dir):
    """
    生成汇总报告
    
    参数:
        results_list: 分析结果列表
        output_dir: 输出目录
    """
    logger = logging.getLogger(__name__)
    
    if not results_list:
        logger.warning("没有有效的分析结果")
        return
    
    # 统计改善情况
    improvements = {
        'edge_density': [],
        'edge_fragments_reduction': [],
        'wall_prominence': []
    }
    
    successful_scenes = []
    failed_scenes = []
    
    for result in results_list:
        if result is None:
            failed_scenes.append("Unknown")
            continue
            
        successful_scenes.append(result['scene_id'])
        improvements['edge_density'].append(result['improvements']['edge_density_improvement'])
        improvements['edge_fragments_reduction'].append(result['improvements']['edge_fragments_reduction'])
        improvements['wall_prominence'].append(result['improvements']['wall_prominence_improvement'])
    
    # 计算统计信息
    stats = {}
    for metric, values in improvements.items():
        if values:
            stats[metric] = {
                'mean': np.mean(values),
                'std': np.std(values),
                'min': np.min(values),
                'max': np.max(values),
                'positive_ratio': np.sum(np.array(values) > 0) / len(values)
            }
    
    # 生成报告
    report = {
        'summary': {
            'total_scenes_tested': len(results_list),
            'successful_scenes': len(successful_scenes),
            'failed_scenes': len(failed_scenes),
            'success_rate': len(successful_scenes) / len(results_list) if results_list else 0
        },
        'improvements_statistics': stats,
        'detailed_results': results_list
    }
    
    # 保存JSON报告
    with open(os.path.join(output_dir, 'enhancement_summary_report.json'), 'w') as f:
        json.dump(report, f, indent=2)
    
    # 生成可视化报告
    if stats:
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 边缘密度改善分布
        axes[0, 0].hist(improvements['edge_density'], bins=20, alpha=0.7, color='blue')
        axes[0, 0].axvline(0, color='red', linestyle='--', alpha=0.7)
        axes[0, 0].set_title(f'Edge Density Improvement\n(Positive: {stats["edge_density"]["positive_ratio"]:.1%})')
        axes[0, 0].set_xlabel('Improvement')
        axes[0, 0].set_ylabel('Frequency')
        
        # 边缘片段减少分布
        axes[0, 1].hist(improvements['edge_fragments_reduction'], bins=20, alpha=0.7, color='green')
        axes[0, 1].axvline(0, color='red', linestyle='--', alpha=0.7)
        axes[0, 1].set_title(f'Edge Fragments Reduction\n(Positive: {stats["edge_fragments_reduction"]["positive_ratio"]:.1%})')
        axes[0, 1].set_xlabel('Reduction')
        axes[0, 1].set_ylabel('Frequency')
        
        # 墙体突出度改善分布
        axes[1, 0].hist(improvements['wall_prominence'], bins=20, alpha=0.7, color='orange')
        axes[1, 0].axvline(0, color='red', linestyle='--', alpha=0.7)
        axes[1, 0].set_title(f'Wall Prominence Improvement\n(Positive: {stats["wall_prominence"]["positive_ratio"]:.1%})')
        axes[1, 0].set_xlabel('Improvement')
        axes[1, 0].set_ylabel('Frequency')
        
        # 综合改善散点图
        wall_improvements = improvements['wall_prominence']
        edge_improvements = improvements['edge_density']
        
        axes[1, 1].scatter(wall_improvements, edge_improvements, alpha=0.6)
        axes[1, 1].axhline(0, color='red', linestyle='--', alpha=0.7)
        axes[1, 1].axvline(0, color='red', linestyle='--', alpha=0.7)
        axes[1, 1].set_xlabel('Wall Prominence Improvement')
        axes[1, 1].set_ylabel('Edge Density Improvement')
        axes[1, 1].set_title('Improvement Correlation')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'enhancement_statistics.png'), dpi=150, bbox_inches='tight')
        plt.close()
    
    # 打印摘要
    logger.info("=" * 80)
    logger.info("投影增强效果测试汇总报告")
    logger.info("=" * 80)
    logger.info(f"测试场景总数: {report['summary']['total_scenes_tested']}")
    logger.info(f"成功场景数: {report['summary']['successful_scenes']}")
    logger.info(f"失败场景数: {report['summary']['failed_scenes']}")
    logger.info(f"成功率: {report['summary']['success_rate']:.1%}")
    
    if stats:
        logger.info("\n改善效果统计:")
        for metric, stat in stats.items():
            logger.info(f"  {metric}:")
            logger.info(f"    平均改善: {stat['mean']:.4f} ± {stat['std']:.4f}")
            logger.info(f"    改善比例: {stat['positive_ratio']:.1%}")
            logger.info(f"    范围: [{stat['min']:.4f}, {stat['max']:.4f}]")
    
    logger.info(f"\n详细报告保存到: {output_dir}")

def main():
    parser = argparse.ArgumentParser(description='Batch test point cloud projection enhancements')
    parser.add_argument('--data_root', required=True, help='Path to Structured3D data root')
    parser.add_argument('--output_dir', default='tools/debug/batch_enhancement_test', help='Output directory')
    parser.add_argument('--max_scenes', type=int, default=10, help='Maximum number of scenes to test')
    parser.add_argument('--skip_existing', action='store_true', help='Skip scenes that already have results')
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    logger.info(f"开始批量测试投影增强效果")
    logger.info(f"数据根目录: {args.data_root}")
    logger.info(f"输出目录: {args.output_dir}")
    logger.info(f"最大测试场景数: {args.max_scenes}")
    
    # 查找点云文件
    logger.info("查找点云文件...")
    point_cloud_files = find_point_cloud_files(args.data_root, args.max_scenes)
    logger.info(f"找到 {len(point_cloud_files)} 个点云文件")
    
    if not point_cloud_files:
        logger.error("未找到任何点云文件")
        return 1
    
    # 批量处理
    results_list = []
    
    for ply_path, scene_id in tqdm(point_cloud_files, desc="Processing scenes"):
        try:
            # 检查是否已存在结果
            if args.skip_existing:
                analysis_file = os.path.join(args.output_dir, f"{scene_id}_analysis.json")
                if os.path.exists(analysis_file):
                    logger.info(f"跳过已存在的场景: {scene_id}")
                    with open(analysis_file, 'r') as f:
                        results_list.append(json.load(f))
                    continue
            
            logger.info(f"处理场景: {scene_id}")
            result = compare_projections(ply_path, args.output_dir, scene_id)
            results_list.append(result)
            
        except Exception as e:
            logger.error(f"处理场景 {scene_id} 失败: {e}")
            results_list.append(None)
    
    # 生成汇总报告
    logger.info("生成汇总报告...")
    generate_summary_report(results_list, args.output_dir)
    
    logger.info("批量测试完成！")
    return 0

if __name__ == "__main__":
    exit(main())
