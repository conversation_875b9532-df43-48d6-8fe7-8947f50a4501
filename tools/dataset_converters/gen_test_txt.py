#!/usr/bin/env python3
"""
Test Set File List Generator for HC3D Dataset

This script generates a test set file list by identifying files that are not
included in either the training or validation sets from the complete dataset.

The script reads three input files:
- all.txt: Complete list of all dataset files
- train.txt: List of files assigned to training set
- val.txt: List of files assigned to validation set

It then generates test.txt containing files that appear in all.txt but not
in either train.txt or val.txt, sorted alphabetically.

Author: Generated for Mask2Former_v2 project
"""

import argparse
import os
import sys
from typing import Set, List


def load_file_list(file_path: str) -> Set[str]:
    """
    Load a list of filenames from a text file.
    
    Args:
        file_path: Path to the text file containing filenames (one per line)
        
    Returns:
        Set of filenames (stripped of whitespace)
        
    Raises:
        FileNotFoundError: If the input file doesn't exist
        IOError: If there's an error reading the file
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Input file not found: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            filenames = {line.strip() for line in f if line.strip()}
        print(f"Loaded {len(filenames)} filenames from {file_path}")
        return filenames
    except IOError as e:
        raise IOError(f"Error reading file {file_path}: {e}")


def save_file_list(file_path: str, filenames: List[str]) -> None:
    """
    Save a list of filenames to a text file.
    
    Args:
        file_path: Path to the output text file
        filenames: List of filenames to save (one per line)
        
    Raises:
        IOError: If there's an error writing the file
    """
    try:
        # Ensure output directory exists
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            for filename in filenames:
                f.write(f"{filename}\n")
        print(f"Saved {len(filenames)} filenames to {file_path}")
    except IOError as e:
        raise IOError(f"Error writing file {file_path}: {e}")


def generate_test_set(all_files: Set[str], train_files: Set[str], val_files: Set[str]) -> List[str]:
    """
    Generate test set by excluding training and validation files from all files.
    
    Args:
        all_files: Set of all dataset filenames
        train_files: Set of training filenames
        val_files: Set of validation filenames
        
    Returns:
        Sorted list of test set filenames
    """
    # Find files that are in all_files but not in train or val
    test_files = all_files - train_files - val_files
    
    # Sort alphabetically
    test_files_sorted = sorted(list(test_files))
    
    print(f"Dataset split summary:")
    print(f"  Total files: {len(all_files)}")
    print(f"  Training files: {len(train_files)}")
    print(f"  Validation files: {len(val_files)}")
    print(f"  Test files: {len(test_files_sorted)}")
    
    # Verify no overlap between sets
    train_val_overlap = train_files & val_files
    if train_val_overlap:
        print(f"Warning: {len(train_val_overlap)} files appear in both train and val sets")
    
    # Check for files in train/val that are not in all
    train_not_in_all = train_files - all_files
    val_not_in_all = val_files - all_files
    
    if train_not_in_all:
        print(f"Warning: {len(train_not_in_all)} training files not found in all.txt")
    if val_not_in_all:
        print(f"Warning: {len(val_not_in_all)} validation files not found in all.txt")
    
    return test_files_sorted


def main():
    """Main function to generate test set file list."""
    parser = argparse.ArgumentParser(
        description='Generate test set file list for HC3D dataset',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        '--filelists_dir',
        default='/home/<USER>/data/RS10_data/00_dataset_spilt/filelists',
        type=str,
        help='Directory containing the input file lists'
    )
    
    parser.add_argument(
        '--all_file',
        default='all.txt',
        type=str,
        help='Filename of the complete dataset file list (relative to filelists_dir)'
    )
    
    parser.add_argument(
        '--train_file',
        default='train.txt',
        type=str,
        help='Filename of the training set file list (relative to filelists_dir)'
    )
    
    parser.add_argument(
        '--val_file',
        default='val.txt',
        type=str,
        help='Filename of the validation set file list (relative to filelists_dir)'
    )
    
    parser.add_argument(
        '--test_file',
        default='test.txt',
        type=str,
        help='Filename of the output test set file list (relative to filelists_dir)'
    )
    
    args = parser.parse_args()
    
    # Construct full file paths
    all_file_path = os.path.join(args.filelists_dir, args.all_file)
    train_file_path = os.path.join(args.filelists_dir, args.train_file)
    val_file_path = os.path.join(args.filelists_dir, args.val_file)
    test_file_path = os.path.join(args.filelists_dir, args.test_file)
    
    try:
        print("Loading dataset file lists...")
        
        # Load input files
        all_files = load_file_list(all_file_path)
        train_files = load_file_list(train_file_path)
        val_files = load_file_list(val_file_path)
        
        # Generate test set
        print("\nGenerating test set...")
        test_files = generate_test_set(all_files, train_files, val_files)
        
        # Save test set
        print(f"\nSaving test set to {test_file_path}...")
        save_file_list(test_file_path, test_files)
        
        print(f"\nTest set generation completed successfully!")
        print(f"Generated {len(test_files)} test files")
        
    except (FileNotFoundError, IOError) as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
